<script lang="ts" setup>
import { ref, onMounted, watch, computed, onBeforeMount } from 'vue'
import Home from './home/<USER>'
import Icon from '@/components/common/Icon.vue'
import ReportList from './report/ReportList.vue'
import AttentionList from './attention/AttentionList.vue'
import My from './my/My.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { useRoute } from 'vue-router'
import systemService from '@/service/systemService'

defineOptions({
    name: 'MainPage',
})
const route = useRoute()
onBeforeMount(() => {
    if (route.query.active) {
        active.value = Number(route.query.active)
    }

    let oemKey = ''
    console.log('route', route.query)
    console.log('oemInfo',oemInfo.value)

    if(route.query.c){
        oemKey = route.query.c as string
        systemService.systemOEMDetail({key:oemKey,productType:1}).then((res) => {
            // console.log('oemInfo1',res.data)
            if(res.success && res.data) {
                store.dispatch('auth/setOemConfig', res.data)
                sessionStorage.setItem('oemConfig', JSON.stringify(res.data.modules[0]))
                sessionStorage.setItem('c-oemkey', String(route.query.c))
            }
        })
    }else{
        if(sessionStorage.getItem('oemConfig') || oemInfo.value){
            return
        }
        systemService.systemOEMDetail({key:oemKey,productType:1}).then((res) => {
            // console.log('oemInfo1',res.data)
            if(res.success && res.data) {
                store.dispatch('auth/setOemConfig', res.data)
                if(res.data.key){
                    // 通过域名OEM标记
                    sessionStorage.setItem('oemConfig', JSON.stringify(res.data.modules[0]))
                    sessionStorage.setItem('ym-oemkey', res.data.key)
                }
            }
        })
        // if(!oemInfo.value){
        //     if(sessionStorage.getItem('oemConfig')){
        //         oemKey = JSON.parse(sessionStorage.getItem('oemConfig') as string).key
        //     }
        //     if(route.query.c){
        //         oemKey = route.query.c as string
        //     }
        //     systemService.systemOEMDetail({key:oemKey,productType:1}).then((res) => {
        //         // console.log('oemInfo1',res.data)
        //         if(res.success && res.data) {
        //             store.dispatch('auth/setOemConfig', res.data)
        //             sessionStorage.setItem('oemConfig', JSON.stringify(res.data))
        //         }
        //     })
        // }
    }
})


const active = ref(Number(localStorage.getItem('main-active-tab') || 0))

const store = useStore<RootState>()

// 保存当前选中的tab
watch(active, (newVal) => {
    localStorage.setItem('main-active-tab', String(newVal))
})

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

watch(oemInfo, () => {
    initTabs()
})

onMounted(() => {
    initTabs()
})

const tabs = ref<{ id: number, label: string, icon: string }[]>([])
const initTabs = () => {
    const defaultTabs = [
        {
            id: 0,
            label: '首页',
            icon: 'icon-shouye',
        },
        {
            id: 1,
            label: '关注',
            icon: 'icon-a-Component30',
        },
        {
            id: 2,
            label: '报告',
            icon: 'icon-a-Component31',
        },
        {
            id: 3,
            label: '我的',
            icon: 'icon-a-Component32',
        },
    ] as { id: number, label: string, icon: string }[]
    // console.log('oemInfo3',oemInfo.value)
    if (!oemInfo.value?.easyCollect) {
        tabs.value = defaultTabs
    } else {
        tabs.value = defaultTabs.filter(item => item.id === 0 || item.id === 3)
    }
}



</script>

<template>
    <div class="main">
        <div class="container">
            <Home v-if="active === 0" />
            <AttentionList v-if="active === 1 && !oemInfo?.easyCollect" />
            <ReportList v-if="active === 2 && !oemInfo?.easyCollect" />
            <My v-if="active === 3" />
        </div>
        <van-tabbar v-model="active" :safe-area-inset-bottom="true" id="main-tabbar">
            <van-tabbar-item v-for="(item) in tabs" :key="item.id" @click="active = item.id">
                <span :class="{
                    active: active === item.id,
                }">
                    {{ item.label }}
                </span>
                <template #icon="props">
                    <Icon style="cursor: pointer" :icon="item.icon"
                          :color="`${props.active ? 'var(--main-blue-)' : 'var(--table-bg-)'}`" :size="20" />
                </template>
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<style lang="scss" scoped>
.main {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
}

.container {
    flex: 1;
    overflow: auto;
}

:deep(.van-tabbar__placeholder) {
    background-color: transparent !important;
}

.active {
    color: var(--main-blue-);
    font-weight: 500;
}

.un-active {
    color: var(--table-bg-);
}

.label {
    color: var(--table-bg-);
}
</style>
