<template>
    <div class="all-padding-16 oh border-box" style="background-color: #ffffff">
        <div class="display-flex top-bottom-center font-18 space-between b-margin-16">
            <span class="font-weight-600">销售动态</span>
            <div class="display-flex gap-4">
                <van-icon name="edit" color="#3C74EB" size="24" />
                <span class="color-blue">写跟进</span>
            </div>
        </div>
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <van-cell v-if="dataList && dataList.length > 0" v-for="(item,index) in dataList.slice(0,2)" :key="index" >
                <div  style="text-align: left;">
                    <div class="display-flex top-bottom-center gap-4">
                        <div style="height: 0.2rem; width: 0.2rem; border-radius: 50%; border: 2px solid #2b83fd"></div>
                        <div class="font-16 color-black">{{ moment(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                    </div>
                    <div class="l-margin-16 display-flex top-bottom-center color-black " v-if="item.description" style="width: 100%;">
                        {{ item.description }}
                    </div>
                    <div class="l-margin-16 display-flex top-bottom-center">
						<div class="display-flex top-bottom-center text-nowrap font-16">
                            来自{{ item.referType == 'lead' ? '线索' : item.referType == 'customer' ? '客户' : '-' }}:{{ item.referName }}
						</div>
					</div>
                </div>
            </van-cell>
            <div v-if="dataList && dataList.length > 0" class="font-16 flex-center tb-margin-12">暂无数据</div>
            <div class="flex-center font-16 color-blue">
                查看更多
                <van-icon name="arrow" size="18" color="#3C74EB" />
            </div>
        </van-list>

    </div>
</template>

<script lang='ts' setup>
import crmService from '@/service/crmService'
import type { ICrmGetActiviesParams, ICrmGetActiviesItem } from '@/types/lead'
import { ref, onMounted, getCurrentInstance } from 'vue'

const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment

const loading = ref<boolean>(false)
const finished = ref<boolean>(false)
const dataList = ref<ICrmGetActiviesItem[]>([])
const leadId = '1958849538943840258'
const queryParams = ref<ICrmGetActiviesParams>({
    leadId:leadId,
    page:1,
    pageSize:10,
})

let ti = null
const onLoad = () => {
    
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        // console.log('benefitList.value.length',dataList.value.length)
        finished.value = true
    },100)
}

const search = async (params: ICrmGetActiviesParams) => {
    const res = await crmService.crmGetActivities(params)
    dataList.value.push(...res.data)
    return res
}

onMounted(() => {

})

</script>

<style lang='scss' scoped>
:deep(.van-list__finished-text){
    display: none !important;
}
</style>