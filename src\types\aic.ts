import type { IAllR<PERSON>ord } from '@/types/record'
import type { IPaginationResponse, ICommonResponse } from './axios'
import type { ISearchConditions } from '@/types/model'

export interface Region extends IAicConditionDataOptionItem {
    firstLetter?: string
    parent?: Region
}

export interface IAicRegionResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: Region[]
}

export interface IAicConditionDataRequest extends IAllRecord {
    key?: string
}

export interface IAicConditionData {
    b2bMarketType: IAicConditionDataOptionItem[]
    caseReason: IAicConditionDataOptionItem[]
    certType: IAicConditionDataOptionItem[]
    area: IAicConditionDataOptionItem[]
    companySiteSourceMap: IAicConditionDataOptionItem[]
    contactSource: IAicConditionDataOptionItem[]
    cusIndusCategory: IAicConditionDataOptionItem[]
    fixedSource: IAicConditionDataOptionItem[]
    industry: IAicConditionDataOptionItem[]
    industryEn: IAicConditionDataOptionItem[]
    latest3MonthCertL2Type: IAicConditionDataOptionItem[]
    mobileSource: IAicConditionDataOptionItem[]
    serviceNode: IAicConditionDataOptionItem[]
    tenderProjectType: IAicConditionDataOptionItem[]
    tradeMarkCategory: IAicConditionDataOptionItem[]
}

export interface IAicConditionDataOptionItem {
    children?: IAicConditionDataOptionItem[]
    label: string
    pinyin: string
    value: string
}

export interface IAicNormalSearchRulesSysResponse {
    errCode: number
    errMsg: string
    success: boolean
    data: IAicNormalSearchRules[]
}

export interface IAicNormalSearchRules {
    dataType: 'multiSelect' | 'select' | 'mapped' | 'area' | 'mulipleProvince'
    key: string
    name: string
    enums: IAicNormalSearchRuleEnums[]
    needSearch?: string
    paramType?: string
}

export interface IAicNormalSearchRuleEnums {
    name: string
    tagValue: string
}

export interface INormalFilterParams {
    label: string
    value: string
    category?: string
    type?: 'multiSelect' | 'select' | 'mapped' | 'area' | 'mulipleProvince'
    categoryKey: string
    checked?: boolean
    name?: string
    num?: string
    pinyin?: string
    paramType?: string
}

export interface AddProjectParams {
    categoryId?: string
    name: string
    parentId?: string
    shortName?: string
}

export interface ISearchListParams extends IAllRecord {
    page: number
    pageSize: number
    id?: string
}

export interface ModelGetCategoryListResponse extends IPaginationResponse {
    data: CatagoryItem[]
}

export interface CatagoryItem {
    id: string
    name: string
    parentId?: string
    shortName?: string
    createTime?: string
    level?: number
    isNormal?: boolean
    disabled?: boolean
    collapsed?: boolean
    children?: CatagoryItem[]
}

export interface CategoryItemResponse extends ICommonResponse {
    data: CatagoryItem[]
}

export interface SaveTempleteParams {
    name?: string
    categoryIds?: string[]
    searchData?: {
        list: ISearchConditions[]
    }
}

export interface UpdateTemplateParams extends SaveTempleteParams {
    templateId: string
    useTag?: boolean
    sort?: number
}

export interface GsGetPersonEnterpriseRelationsParams extends IAllRecord {
    entId: string
    name: string
    companyName: string
    page: number
}

interface entTagsItem {
    tagName: string
    category: string
    tagCode: string
    categoryCode: string
}

export interface PersonEnterpriseRelationsItem extends IAllRecord {
    address: string
    entName: string
    entTags: entTagsItem[]
    pid: string
    position: string
    positionStatus: string
    regCapital: string
    shareholdingRatio: string
}

export interface GsGetPersonEnterpriseRelationsResponse {
    data: {
        items: PersonEnterpriseRelationsItem[]
        total: number
    }
}

export interface ICenterEntParams {
    circle: {
        center: string
        radius: number
    }
    contact?: string[]
    entstatus?: string[]
    enttype?: string[]
    establishment?: string[]
    industry?: string[]
    keyword?: string
    location?: string[]
    page: number
    pageSize: number
    registercapital?: string[]
    scope?: string
}

export interface ICenterEntResponse {
    data: []
    page: number
    pageSize: number
    total: number
}
