<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
            <van-cell v-for="item in relativeCompanyList" :key="item.pid">
                <div class="display-flex flex-column" style="text-align: left;">
                    <div class="display-flex gap-8">
                        <img src="@/assets/hub-images/relative-company-touxiang.png" alt="" width="50">
                        <div class="flex-center flex-column">
                            <span class="font-16 font-weight-600 color-black">{{ item.entName }}</span>
                            <!-- <div v-for="tag in item.entTags" :key="tag.tagCode"></div> -->
                        </div>
                    </div>

                </div>
            </van-cell>
        </van-list>
    </div>
</template>

<script lang='ts' setup>
import { ref, computed } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'
import type { GsGetPersonEnterpriseRelationsParams, PersonEnterpriseRelationsItem } from '@/types/aic'
import aicService from '@/service/aicService'

const relativeCompanyList = ref<PersonEnterpriseRelationsItem[]>([])
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

const queryParams = ref<GsGetPersonEnterpriseRelationsParams>({
    page: 1,
    entId: 'd6577f2a93b529c3690887844a3fb2ae',
    name: '张亮',
    companyName: '阿里云计算有限公司',
})

const search = async (params: GsGetPersonEnterpriseRelationsParams) => {
    const res = await aicService.gsGetPersonEnterpriseRelations(params)
    relativeCompanyList.value.push(...res.data.items)
    return res
}

let ti = null
const onLoad = () => {
    
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        const res = await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        console.log('relativeCompanyList.value.length',relativeCompanyList.value.length)
        if (relativeCompanyList.value.length === res.data.total) {
            finished.value = true
        }
    },100)
}

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}
</style>