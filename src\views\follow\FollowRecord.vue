<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <div class="display-flex space-between align-center font-16">
            <span class="font-weight-600">数族（南京）科技有限公司</span>
            <span class="">共
                <span class="color-blue">20</span>
                条
            </span>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { computed } from 'vue'
import { tabbarheight } from '@/utils/tabbar-height'

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}
</style>